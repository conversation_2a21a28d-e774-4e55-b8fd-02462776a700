import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { PageTabs } from "@saas/shared/components/PageTabs";
import { CustomerMetrics } from "@saas/customers/components/CustomerMetrics";
import { CustomersOverview } from "@saas/customers/components/CustomersOverview";
import { CustomersTable } from "@saas/customers/components/CustomersTable";
import { CustomersReports } from "@saas/customers/components/CustomersReports";
import { Button } from "@ui/components/button";
import { PlusIcon, DownloadIcon, MailIcon } from "lucide-react";
import { notFound } from "next/navigation";

export default async function CustomersPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const tabs = [
    {
      value: "overview",
      label: "Visão Geral",
      content: <CustomersOverview organizationId={organization.id} />,
    },
    {
      value: "customers",
      label: "Clientes",
      badge: "1,371",
      content: <CustomersTable organizationId={organization.id} />,
    },
    {
      value: "reports",
      label: "Relatórios",
      content: <CustomersReports organizationId={organization.id} />,
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Clientes"
        subtitle="Gerencie seus clientes e acompanhe métricas importantes"
        actions={
          <>
            <Button variant="outline">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <MailIcon className="h-4 w-4 mr-2" />
              Email em Massa
            </Button>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Novo Cliente
            </Button>
          </>
        }
      />

      <PageTabs tabs={tabs} defaultValue="overview" />
    </div>
  );
}
