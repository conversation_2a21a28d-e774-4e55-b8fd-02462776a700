import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { UsersIcon, BuildingIcon, CreditCardIcon, ActivityIcon, TrendingUpIcon, DollarSignIcon } from "lucide-react";
import { redirect } from "next/navigation";
import { countAllUsers, getOrganizations } from "@repo/database";
import { db } from "@repo/database/prisma/client";
import { isAdmin } from "@repo/auth/lib/helper";

export default async function AdminPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	const [organizations, totalUsers, totalTransactions, monthlyRevenue] = await Promise.all([
		getOrganizations({ limit: 1000, offset: 0 }),
		countAllUsers(),
		db.transaction.count(),
		db.transaction.aggregate({
			where: {
				createdAt: {
					gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
				},
				status: "COMPLETED",
			},
			_sum: { amountCents: true },
		}),
	]);

	const stats = [
		{
			title: "Organizações",
			value: organizations.length,
			description: "Total de organizações",
			icon: BuildingIcon,
			trend: "+12%",
			trendUp: true,
		},
		{
			title: "Usuários",
			value: totalUsers,
			description: "Total de usuários",
			icon: UsersIcon,
			trend: "+8%",
			trendUp: true,
		},
		{
			title: "Transações",
			value: totalTransactions,
			description: "Total de transações",
			icon: CreditCardIcon,
			trend: "+23%",
			trendUp: true,
		},
		{
			title: "Receita Mensal",
			value: `R$ ${((monthlyRevenue._sum?.amountCents || 0) / 100).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
			description: "Receita do mês atual",
			icon: DollarSignIcon,
			trend: "+15%",
			trendUp: true,
		},
	];

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Dashboard Administrativo</h1>
				<p className="text-muted-foreground">
					Visão geral do sistema e métricas principais
				</p>
			</div>

			{/* Stats Grid */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{stats.map((stat) => {
					const Icon = stat.icon;
					return (
						<Card key={stat.title}>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">
									{stat.title}
								</CardTitle>
								<Icon className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">{stat.value}</div>
								<p className="text-xs text-muted-foreground">
									{stat.description}
								</p>
								<div className="flex items-center pt-1">
									<TrendingUpIcon className={`h-3 w-3 ${stat.trendUp ? 'text-green-500' : 'text-red-500'}`} />
									<span className={`text-xs ml-1 ${stat.trendUp ? 'text-green-500' : 'text-red-500'}`}>
										{stat.trend}
									</span>
									<span className="text-xs text-muted-foreground ml-1">
										vs mês anterior
									</span>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>

			{/* Recent Activity */}
			<div className="grid gap-4 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<ActivityIcon className="h-5 w-5" />
							Atividade Recente
						</CardTitle>
						<CardDescription>
							Últimas ações no sistema
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="flex items-center space-x-4">
								<div className="w-2 h-2 bg-green-500 rounded-full"></div>
								<div className="flex-1 space-y-1">
									<p className="text-sm font-medium">Nova organização criada</p>
									<p className="text-xs text-muted-foreground">Academia Digital - há 2 horas</p>
								</div>
							</div>
							<div className="flex items-center space-x-4">
								<div className="w-2 h-2 bg-blue-500 rounded-full"></div>
								<div className="flex-1 space-y-1">
									<p className="text-sm font-medium">Usuário registrado</p>
									<p className="text-xs text-muted-foreground">João Silva - há 4 horas</p>
								</div>
							</div>
							<div className="flex items-center space-x-4">
								<div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
								<div className="flex-1 space-y-1">
									<p className="text-sm font-medium">Transação processada</p>
									<p className="text-xs text-muted-foreground">R$ 297,00 - há 6 horas</p>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<TrendingUpIcon className="h-5 w-5" />
							Métricas de Crescimento
						</CardTitle>
						<CardDescription>
							Performance do sistema
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="flex justify-between items-center">
								<span className="text-sm">Novos usuários (7 dias)</span>
								<span className="text-sm font-medium text-green-500">+24</span>
							</div>
							<div className="flex justify-between items-center">
								<span className="text-sm">Organizações ativas</span>
								<span className="text-sm font-medium text-blue-500">12</span>
							</div>
							<div className="flex justify-between items-center">
								<span className="text-sm">Taxa de conversão</span>
								<span className="text-sm font-medium text-purple-500">3.2%</span>
							</div>
							<div className="flex justify-between items-center">
								<span className="text-sm">Receita total</span>
								<span className="text-sm font-medium text-green-500">R$ 15.420</span>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
