import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { ProductConfigurationClient } from "./components/ProductConfigurationClient";
import { PageLayout } from "@ui/components/page-layout";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, EyeIcon } from "lucide-react";
import Link from "next/link";

interface PageProps {
  params: Promise<{
    productId: string;
  }>;
}

export default async function ProductConfigurationPage({ params }: PageProps) {
  const { productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      creatorId: session.user.id,
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect("/app/products");
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Configurações: {product.name}</h1>
          <p className="text-muted-foreground">Configure as definições e preferências do seu produto</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/app/products/${product.id}`}>
              <EyeIcon className="h-4 w-4 mr-2" />
              Visualizar
            </Link>
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href="/app/products">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Voltar
            </Link>
          </Button>
        </div>
      </div>

      <div className="bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300 dark:border-yellow-700 rounded-lg p-4">
        <p className="text-yellow-800 dark:text-yellow-200 text-sm font-medium">
          🎯 TESTE DE LAYOUT: Se você está vendo esta mensagem dentro de um card com sidebar à esquerda, o AppWrapper está funcionando corretamente!
        </p>
      </div>

      <ProductConfigurationClient
        product={product}
        organizationId={product.organizationId}
      />
    </div>
  );
}
