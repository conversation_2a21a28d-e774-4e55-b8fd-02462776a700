"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@ui/lib";
import {
	UsersIcon,
	BuildingIcon,
	SettingsIcon,
	LayoutDashboard,
	CreditCardIcon,
	ActivityIcon,
	FileTextIcon,
} from "lucide-react";

const adminMenuItems = [
	{
		label: "Dashboard",
		href: "/app/backoffice",
		icon: LayoutDashboard,
	},
	{
		label: "Organizações",
		href: "/app/backoffice/organizations",
		icon: BuildingIcon,
	},
	{
		label: "Usuários",
		href: "/app/backoffice/users",
		icon: UsersIcon,
	},
	{
		label: "Logs",
		href: "/app/backoffice/logs",
		icon: FileTextIcon,
	},
	{
		label: "Configurações",
		href: "/app/backoffice/settings",
		icon: SettingsIcon,
	},
];

export function AdminSidebar() {
	const pathname = usePathname();

	return (
		<aside className="w-64 space-y-6">
			<div>
				<h2 className="text-lg font-semibold text-foreground">
					Administração
				</h2>
				<p className="text-sm text-muted-foreground">
					Gerencie seu sistema
				</p>
			</div>

			<nav className="space-y-2">
				{adminMenuItems.map((menuItem) => (
					<Link
						key={menuItem.href}
						href={menuItem.href}
						className={cn(
							"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
							pathname === menuItem.href
								? "bg-primary text-primary-foreground"
								: "text-muted-foreground hover:text-foreground hover:bg-muted"
						)}
					>
						<menuItem.icon className="h-4 w-4" />
						{menuItem.label}
					</Link>
				))}
			</nav>

			<div className="pt-6 border-t">
				<Link
					href="/app"
					className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
				>
					← Voltar ao App
				</Link>
			</div>
		</aside>
	);
}
